package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	echoAdapter "github.com/TickLabVN/tonic/adapters/echo"
	"github.com/TickLabVN/tonic/core/docs"
	"github.com/go-playground/validator/v10"
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	"gitlab.zalopay.vn/phunn4/coupon-api-gateway/internal/clients"
	"gitlab.zalopay.vn/phunn4/coupon-api-gateway/internal/handlers"
	custom_middleware "gitlab.zalopay.vn/phunn4/coupon-api-gateway/internal/middleware"
	"gitlab.zalopay.vn/phunn4/coupon-api-gateway/internal/utils"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/auth"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/health"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/tracing"
)

type CustomValidator struct {
	validator *validator.Validate
}

func (cv *CustomValidator) Validate(i any) error {
	if err := cv.validator.Struct(i); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}
	return nil
}

func main() {
	cfg, err := config.Load()
	if err != nil {
		panic(fmt.Sprintf("failed to load config: %v", err))
	}

	logger := logging.New(cfg.Logging.Level, cfg.Logging.Format)
	logger.Infof("Starting service: %s v%s", cfg.Service.Name, cfg.Service.Version)

	tracer, err := tracing.New(cfg.Service.Name, cfg.Jaeger.Host, cfg.Jaeger.Port)
	if err != nil {
		logger.Fatalf("failed to initialize tracer: %v", err)
	}

	appMetrics := metrics.New(cfg.Service.Name)
	jwtManager := auth.NewJWTManager(&cfg.Auth)

	cookieConfig := utils.NewCookieConfig("", false)

	userClient, err := clients.NewUserClient(cfg.DownstreamServices.UserServiceAddr, &cfg.GRPC, logger, appMetrics, cfg.Service.ClientID, cfg.Service.ClientKey)
	if err != nil {
		logger.Fatalf("Failed to create user client: %v", err)
	}

	voucherClient, err := clients.NewVoucherClient(cfg.DownstreamServices.CouponServiceAddr, &cfg.GRPC, logger, appMetrics, cfg.Service.ClientID, cfg.Service.ClientKey)
	if err != nil {
		logger.Fatalf("Failed to create voucher client: %v", err)
	}

	e := echo.New()
	e.Validator = &CustomValidator{validator: validator.New()}

	openapi := &docs.OpenApi{
		OpenAPI: "3.0.1",
		Info: docs.InfoObject{
			Title:   "Coupon Management API",
			Version: "1.0.0",
		},
		Tags: []docs.Tag{
			{Name: "Auth API"},
			{Name: "Users API"},
			{Name: "Vouchers API"},
			{Name: "Orders API"},
			{Name: "Products API"},
		},
	}
	echoAdapter.UIHandle(e, openapi, "/docs")

	e.Use(middleware.Recover())
	e.Use(middleware.CORS())

	authHandler := handlers.NewAuthHandler(userClient, logger, cookieConfig)
	userHandler := handlers.NewUserHandler(userClient, logger)
	voucherHandler := handlers.NewVoucherHandler(voucherClient, logger)

	apiGroup := e.Group("/api")

	authHandler.RegisterPublicRoutes(apiGroup, openapi)
	voucherHandler.RegisterPublicRoutes(apiGroup, openapi)

	cookieAuthConfig := &custom_middleware.CookieAuthConfig{
		JWTManager:   jwtManager,
		CookieConfig: cookieConfig,
		Logger:       logger,
		SkipPaths:    []string{"/api/register", "/api/login", "/api/logout", "/api/health", "/api/metrics"},
	}
	cookieAuthMiddleware := custom_middleware.CookieAuth(cookieAuthConfig)

	protectedGroup := e.Group("/api", cookieAuthMiddleware)
	userHandler.RegisterProtectedRoutes(protectedGroup, openapi)
	voucherHandler.RegisterProtectedRoutes(apiGroup, openapi)

	healthChecker := health.NewHealthChecker()
	e.GET("/health", healthChecker.HTTPHandler())
	e.GET(cfg.Metrics.Path, echo.WrapHandler(appMetrics.Handler()))

	ctx, stop := signal.NotifyContext(context.Background(), os.Interrupt, syscall.SIGTERM)
	defer stop()

	startHTTPServer(ctx, cfg, e, logger)

	tracer.Close()
	userClient.Close()
	logger.Info("Shutdown complete")
}

func startHTTPServer(ctx context.Context, cfg *config.Config, e *echo.Echo, logger *logging.Logger) {
	addr := fmt.Sprintf(":%d", cfg.Service.Port)
	server := &http.Server{Addr: addr, Handler: e}

	logger.Infof("API Gateway starting on %s", addr)
	go func() {
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatalf("API Gateway failed: %v", err)
		}
	}()

	<-ctx.Done()
	shutdownCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	if err := server.Shutdown(shutdownCtx); err != nil {
		logger.Fatalf("API Gateway shutdown failed: %v", err)
	}
}
