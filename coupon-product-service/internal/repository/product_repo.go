package repository

import (
	"context"
	"fmt"
	"strings"

	"gitlab.zalopay.vn/phunn4/coupon-product-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
)

type productInfrastructure struct {
	db     *database.DB
	logger *logging.Logger
}

type ProductRepository interface {
	// Product operations
	CreateProduct(ctx context.Context, product *model.Product) error
	GetProductByID(ctx context.Context, id string) (*model.Product, error)
	UpdateProduct(ctx context.Context, product *model.Product) error
	DeleteProduct(ctx context.Context, id string) error
	ListProducts(ctx context.Context, req *model.ListProductsRequest) ([]*model.Product, int64, error)

	// Category operations
	CreateCategory(ctx context.Context, category *model.Category) error
	GetCategoryByID(ctx context.Context, id string) (*model.Category, error)
	ListCategories(ctx context.Context, req *model.ListCategoriesRequest) ([]*model.Category, int64, error)
}

func NewProductRepository(db *database.DB, logger *logging.Logger) ProductRepository {
	return &productInfrastructure{
		db:     db,
		logger: logger,
	}
}

// Product operations
func (r *productInfrastructure) CreateProduct(ctx context.Context, product *model.Product) error {
	return r.db.WithContext(ctx).Create(product).Error
}

func (r *productInfrastructure) GetProductByID(ctx context.Context, id string) (*model.Product, error) {
	var product model.Product
	err := r.db.WithContext(ctx).Preload("Category").First(&product, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &product, nil
}

func (r *productInfrastructure) UpdateProduct(ctx context.Context, product *model.Product) error {
	return r.db.WithContext(ctx).Save(product).Error
}

func (r *productInfrastructure) DeleteProduct(ctx context.Context, id string) error {
	return r.db.WithContext(ctx).Delete(&model.Product{}, "id = ?", id).Error
}

func (r *productInfrastructure) ListProducts(ctx context.Context, req *model.ListProductsRequest) ([]*model.Product, int64, error) {
	var products []*model.Product
	var total int64

	query := r.db.WithContext(ctx).Model(&model.Product{})

	// Apply filters
	if req.Search != "" {
		searchTerm := "%" + strings.ToLower(req.Search) + "%"
		query = query.Where("LOWER(name) LIKE ? OR LOWER(description) LIKE ?", searchTerm, searchTerm)
	}

	if req.CategoryID != nil && *req.CategoryID != "" {
		query = query.Where("category_id = ?", *req.CategoryID)
	}

	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count products: %w", err)
	}

	// Apply sorting
	sortBy := req.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}
	sortOrder := req.SortOrder
	if sortOrder == "" {
		sortOrder = "desc"
	}
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// Apply pagination
	if req.Page > 0 && req.Limit > 0 {
		offset := (req.Page - 1) * req.Limit
		query = query.Offset(offset).Limit(req.Limit)
	}

	// Execute query with preloading
	if err := query.Preload("Category").Find(&products).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list products: %w", err)
	}

	return products, total, nil
}

// Category operations
func (r *productInfrastructure) CreateCategory(ctx context.Context, category *model.Category) error {
	return r.db.WithContext(ctx).Create(category).Error
}

func (r *productInfrastructure) GetCategoryByID(ctx context.Context, id string) (*model.Category, error) {
	var category model.Category
	err := r.db.WithContext(ctx).First(&category, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &category, nil
}

func (r *productInfrastructure) ListCategories(ctx context.Context, req *model.ListCategoriesRequest) ([]*model.Category, int64, error) {
	var categories []*model.Category
	var total int64

	query := r.db.WithContext(ctx).Model(&model.Category{})

	// Apply search filter
	if req.Search != "" {
		searchTerm := "%" + strings.ToLower(req.Search) + "%"
		query = query.Where("LOWER(name) LIKE ? OR LOWER(description) LIKE ?", searchTerm, searchTerm)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count categories: %w", err)
	}

	// Apply sorting
	query = query.Order("name ASC")

	// Apply pagination
	if req.Page > 0 && req.Limit > 0 {
		offset := (req.Page - 1) * req.Limit
		query = query.Offset(offset).Limit(req.Limit)
	}

	// Execute query
	if err := query.Find(&categories).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list categories: %w", err)
	}

	return categories, total, nil
}
